"""
飞书事件处理器模块
负责处理飞书的各种事件，包括消息接收和卡片操作
"""

import json
import asyncio
import time
import os
import lark_oapi as lark
from lark_oapi.event.callback.model.p2_card_action_trigger import (
    P2CardActionTrigger,
    P2CardActionTriggerResponse,
)
from src.utils.logger import logger
from src.services.chatbot.bad_case_service import mark_bad_case
from src.services.agent.bots.user_query_recommendation_bot import UserQueryRecommendationBot
from src.services.chatbot.history_service import (
    get_user_latest_queries,
    get_other_users_latest_queries,
)
from src.services.feishu.message_apis import (
    reply_simple_text_message,
    after_badcase_mark,
    get_message_image_content,
    send_message_to_chat,
    send_recommendation_card,
)
from .deduplicator import message_deduplicator
from .user_service import UserService
from .message_parser import MessageParser
from .query_processor import QueryProcessor
from .config import FeishuConfig


class EventHandlers:
    """事件处理器类"""

    @staticmethod
    def handle_card_action_trigger(
        data: P2CardActionTrigger,
    ) -> P2CardActionTriggerResponse:
        """处理卡片按钮点击回调

        Args:
            data: 卡片操作触发数据

        Returns:
            P2CardActionTriggerResponse: 响应对象
        """
        logger.info(lark.JSON.marshal(data))
        conversation_id = data.event.action.value.get("conversation_id")
        card_id = data.event.action.value.get("card_id")
        logger.info(f"用户准备将 conversation_id 标记为 badcase:{conversation_id}")

        if conversation_id:
            # 尝试获取用户信息
            user_name = None
            try:
                user_id = data.event.operator.open_id
                if user_id:
                    user_info_str = UserService.get_user_info(user_id)
                    if user_info_str:
                        user_info = json.loads(user_info_str)
                        user_name = user_info.get("name", "飞书用户")
            except Exception as e:
                logger.warning(f"获取用户信息失败: {e}")
                user_name = "飞书用户"

            mark_bad_case(
                conversation_id=conversation_id, is_bad_case=True, user_name=user_name
            )

            # 定义一个异步辅助函数来处理延迟和调用 after_badcase_mark
            async def _delayed_after_badcase_mark_task(current_card_id: str):
                await asyncio.sleep(1.5)  # 异步等待1.5秒
                # after_badcase_mark 是一个阻塞I/O操作（网络请求）
                # 应在线程中运行以避免阻塞 asyncio 事件循环
                await asyncio.to_thread(after_badcase_mark, current_card_id)

            # 创建一个后台任务来执行延迟调用
            if card_id:  # 仅在 card_id 有效时创建任务
                asyncio.create_task(_delayed_after_badcase_mark_task(card_id))
            else:
                logger.warning("card_id 为空，无法调度 after_badcase_mark 的延迟调用")

        resp = {
            "toast": {
                "type": "info",
                "content": "已标记为Badcase，感谢反馈！",
            },
        }
        return P2CardActionTriggerResponse(resp)

    @staticmethod
    async def handle_message_receive(data: lark.im.v1.P2ImMessageReceiveV1) -> None:
        """处理接收到的飞书消息

        Args:
            data: 消息接收数据
        """
        message_data_str = lark.JSON.marshal(data)
        logger.info(f"Async handler processing message: {message_data_str}")

        # 基本验证
        if not data.event.message.content:
            logger.info(f"无需处理: 消息内容为空")
            return

        # 解析消息内容
        content_data = MessageParser.parse_message_content(data.event.message.content)

        # 提取文本内容
        user_query = MessageParser.extract_text_from_content(content_data)
        logger.info(f"提取到的文本内容: {user_query}")

        message_id = data.event.message.message_id
        image_key = MessageParser.extract_image_key_from_content(content_data)
        image_url = None
        if image_key:
            logger.info(f"获取消息图片内容: {message_id}, {image_key}")
            image_url = get_message_image_content(message_id, image_key)
            if image_url and len(image_url)>60 and not "data:image/jpeg;base64," in image_url:
                image_url = f"data:image/jpeg;base64,{image_url}"
            else:
                logger.warning(f"这应该是非法的image_url：{image_url}")
                image_url = None

        # 获取聊天类型和提及信息
        chat_type = data.event.message.chat_type
        mentions = getattr(data.event.message, "mentions", None)

        # 检查是否应该跳过处理
        should_skip, skip_reason = MessageParser.should_skip_message(
            content_data, user_query, chat_type, mentions
        )
        if should_skip:
            logger.info(f"跳过处理: {skip_reason}")
            return

        parent_id = data.event.message.parent_id
        root_id = data.event.message.root_id or message_id

        # 消息去重检查
        if message_deduplicator.is_message_processed(message_id):
            return
        else:
            message_deduplicator.mark_message_processed(message_id)

        # 检查发送者类型
        if data.event.sender.sender_type != "user":
            logger.info(f"忽略非用户消息: sender_type={data.event.sender.sender_type}")
            return

        user_open_id = data.event.sender.sender_id.open_id
        if not user_open_id:
            logger.info(f"无需处理: 无法获取 user_open_id")
            return

        # 获取用户信息
        user_info_str = UserService.get_user_info(user_id=user_open_id)
        if not user_info_str:
            logger.info(f"获取用户失败: user_open_id:{user_open_id}")
            reply_simple_text_message(
                message_id, "抱歉，无法获取您的用户信息，请稍后再试。"
            )
            return

        user_info_dict = json.loads(user_info_str)
        logger.info(f"获取用户成功:{user_info_dict}")

        # 保存用户信息到数据库
        UserService.upsert_user_info_to_db(user_info_dict, user_open_id)

        # 处理用户信息
        user_info_dict = UserService.process_user_info(user_info_dict)

        if not user_query:
            logger.info(f"无需处理: 消息文本为空")
            reply_simple_text_message(message_id, "请输入您的问题。")
            return

        # 清理用户查询
        user_query = MessageParser.clean_user_query(user_query)

        # 验证查询长度（仅对新对话）
        is_new_conversation = root_id == message_id
        if not MessageParser.validate_query_length(user_query, is_new_conversation):
            logger.info(f"用户消息过短: {user_query}")
            reply_text = MessageParser.get_query_too_short_reply()
            reply_simple_text_message(message_id, reply_text)
            return

        # 处理查询
        try:
            asyncio.create_task(
                QueryProcessor.handle_agent_query(
                    message_id=message_id,
                    user_query=user_query.strip(),
                    user_info_dict=user_info_dict,
                    root_id=root_id,
                    parent_id=parent_id,
                    image_url=image_url,
                )
            )
        except Exception as e:
            logger.error(f"启动Agent任务时出错: {e}", exc_info=True)
            reply_simple_text_message(message_id, f"处理您的请求时遇到错误: {e}")
            
    @staticmethod
    async def handle_user_entered(data: lark.im.v1.P2ImChatAccessEventBotP2pChatEnteredV1) -> None:
        """处理用户进入P2P聊天事件

        Args:
            data: 用户进入事件数据
        """
        try:
            # 获取当前时间的时间戳（秒）
            current_time = time.time()
            # 将事件的时间戳从毫秒转换为秒
            last_message_create_time = int(data.event.last_message_create_time) / 1000
            # 配置的时间间隔（默认12小时，单位为秒）
            time_threshold = int(os.getenv("USER_ENTERED_THRESHOLD", "12")) * 3600
            
            data_str = lark.JSON.marshal(data)
            logger.info(f"Async handler processing user entered: {data_str}")

            # 检查事件是否早于当前时间time_threshold小时
            if current_time - last_message_create_time < time_threshold:
                logger.info(f"无需处理用户进入事件: {data.event.last_message_create_time}, 时间间隔为{time_threshold}秒")
                return

            user_open_id = data.event.operator_id.open_id
            user_info_str = UserService.get_user_info(user_open_id)
            if not user_info_str:
                logger.error(f"获取用户失败: user_open_id:{user_open_id}")
                return
            
            user_info = json.loads(user_info_str)
            user_email = user_info.get("email")
            if not user_email:
                logger.error(f"用户邮箱为空，无法生成推荐: user_open_id:{user_open_id}")
                return

            # 1. 获取当前用户的历史消息
            current_user_messages = get_user_latest_queries(user_email=user_email, limit=10)
            logger.info(f"为用户 {user_email} 获取到 {len(current_user_messages)} 条历史消息")

            # 2. 获取其他用户的历史消息
            other_users_messages = get_other_users_latest_queries(current_user_email=user_email, limit=10)
            logger.info(f"为用户 {user_email} 获取到 {len(other_users_messages)} 条其他用户消息作为参考")

            # 如果没有任何历史消息，则不进行推荐
            if not current_user_messages and not other_users_messages:
                logger.info(f"用户 {user_email} 没有任何历史消息，不进行推荐")
                return

            # 3. 调用推荐机器人
            recommendation_bot = UserQueryRecommendationBot(user_info)
            recommendations = await recommendation_bot.get_recommendations(
                current_user_messages=current_user_messages,
                other_users_messages=other_users_messages,
            )

            # 4. 发送推荐卡片
            if recommendations:
                logger.info(f"成功为用户 {user_email} 生成 {len(recommendations)} 条推荐，准备发送卡片")
                await send_recommendation_card(
                    chat_id=data.event.chat_id,
                    user_name=user_info.get("name", "飞书用户"),
                    recommendations=recommendations
                )
            else:
                logger.info(f"未能为用户 {user_email} 生成推荐问题")
        except Exception as e:
            logger.exception(f"处理用户进入事件时出错: {e}")
            return

    @staticmethod
    def sync_wrapper_message_receive(data: lark.im.v1.P2ImMessageReceiveV1) -> None:
        """同步包装器，用于调度异步消息处理器

        Args:
            data: 消息接收数据
        """
        try:
            logger.info(f"同步包装器收到消息: {data.event.message.message_id}")
            # 将异步处理程序调度到事件循环上运行
            asyncio.create_task(EventHandlers.handle_message_receive(data))
        except Exception as e:
            logger.error(f"调度异步消息处理任务时出错: {e}", exc_info=True)

    @staticmethod
    def sync_wrapper_p2p_user_entered(data: lark.im.v1.P2ImChatAccessEventBotP2pChatEnteredV1) -> None:
        """同步包装器，用于接收P2P用户进入事件

        Args:
            data: 消息接收数据
        """
        try:
            logger.info(f"用户进入了和机器人的P2P聊天: chat_id:{data.event.chat_id}, last_message_create_time:{data.event.last_message_create_time}")
            # 将异步处理程序调度到事件循环上运行
            asyncio.create_task(EventHandlers.handle_user_entered(data))
        except Exception as e:
            logger.error(f"调度异步消息处理任务时出错: {e}", exc_info=True)

def create_event_handler():
    """创建事件处理器

    Returns:
        事件处理器实例
    """
    return (
        lark.EventDispatcherHandler.builder("", "")
        .register_p2_im_message_receive_v1(EventHandlers.sync_wrapper_message_receive)
        .register_p2_card_action_trigger(EventHandlers.handle_card_action_trigger)
        .register_p2_im_chat_access_event_bot_p2p_chat_entered_v1(EventHandlers.sync_wrapper_p2p_user_entered)
        .build()
    )
