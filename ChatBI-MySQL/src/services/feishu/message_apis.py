import json
import uuid
import base64
import os
from datetime import datetime
from dataclasses import dataclass
import lark_oapi as lark
from lark_oapi.api.im.v1 import *
from lark_oapi.api.cardkit.v1 import *
from src.utils.logger import logger
from src.services.auth.user_login_with_feishu import APP_ID, APP_SECRET
from .agent_message_formatter import THINKING_PROCESS_SPLITTER

THINKING_LEMENT_ID = "markdown_thinking"
FINAL_LEMENT_ID = "markdown_final_reply"
FOOTNOTE_ELEMENT_ID = "markdown_footnote"
FEEDBACK_ELEMENT_ID = "badcase_feedback"

# 获取Bad Case通知群聊ID
BAD_CASE_NOTIFICATION_CHAT_ID = os.getenv("BAD_CASE_NOTIFICATION_CHAT_ID")

# Create lark client
lark_client = (
    lark.Client.builder()
    .app_id(APP_ID)
    .app_secret(APP_SECRET)
    .timeout(3)
    .log_level(lark.LogLevel.INFO)
    .build()
)

LOG_SERVER_NAME = bool(os.getenv("LOG_SERVER_NAME", False))
# Get server name from command `hostname`
SERVER_NAME = os.popen("hostname").read().strip()

@dataclass
class MessageContent:
    """消息内容"""
    text: str
    image_url: str | None  # base64编码的图片内容，可以用于AI对话

def get_message_content(message_id: str) -> MessageContent | None:
    """获取消息内容，并处理不同格式的返回"""
    logger.info(f"获取消息内容: {message_id}")
    request: GetMessageRequest = (
        GetMessageRequest.builder()
        .message_id(message_id)
        .build()
    )
    response: GetMessageResponse = lark_client.im.v1.message.get(request)

    if not response.success():
        logger.error(f"获取消息内容失败: {response.code} {response.msg} {response.raw.content}")
        return None

    if not response.data.items:
        logger.warning(f"消息 {message_id} 内容为空.")
        return MessageContent(text="", image_url=None)

    item = response.data.items[0]
    msg_type = item.msg_type
    body_content_str = item.body.content

    texts = []
    image_key = None
    image_url_b64 = None

    try:
        if body_content_str:
            content_json = json.loads(body_content_str)

            if msg_type == "text":
                if "text" in content_json:
                    texts.append(content_json["text"])
            elif msg_type == "image":
                if "image_key" in content_json:
                    image_key = content_json["image_key"]
            elif msg_type == "post":
                # "post" 类型的 content 是一个包含 title 和 content 的JSON字符串
                # content_json["content"] 是一个二维数组，代表富文本内容
                if "content" in content_json and isinstance(content_json["content"], list):
                    for block in content_json["content"]:
                        if isinstance(block, list):
                            for element in block:
                                if isinstance(element, dict):
                                    tag = element.get("tag")
                                    if tag == "text":
                                        texts.append(element.get("text", ""))
                                    elif tag == "img":
                                        image_key = element.get("image_key") # 保留最后一个图片
            else:
                # 对于其他未知或未显式处理的类型，如果 content 是简单文本，则尝试直接使用
                # 或者如果它是一个JSON但不是我们期望的结构，我们可能无法从中提取文本/图片
                # 但为了兼容旧的逻辑 text=response.data.items[0].body.content
                # 如果它不是有效的json或者不是特定类型，就直接当作text
                try:
                    # 尝试看 content_json 是否就是文本内容本身 (不太可能，因为上面已经json.loads了)
                    # 但如果 msg_type 不是以上几种，原始 content 可能就是文本
                    # 这种情况已经在最开始的 json.loads(body_content_str) 处理了，如果body_content_str不是json，会报错
                    # 更安全的方式是，如果类型不匹配，并且无法解析为已知结构，则 texts 保持为空
                    pass # 已经在上面处理了特定类型
                except json.JSONDecodeError:
                    texts.append(body_content_str) # 如果原始内容不是JSON，直接视为文本

        # 如果直接从 body 获取到 image_key (例如一些简单图片消息可能直接有这个字段)
        if hasattr(item.body, 'image_key') and item.body.image_key:
            image_key = item.body.image_key

    except json.JSONDecodeError:
        logger.warning(f"消息 {message_id} 的 body.content 不是有效的JSON字符串: {body_content_str}, 将其视为纯文本.")
        texts.append(body_content_str) # 如果无法解析JSON，将整个content视为文本
    except Exception as e:
        logger.error(f"解析消息 {message_id} 内容时出错: {e}", exc_info=True)
        # 即使解析出错，也尝试使用原始 content 作为文本
        if body_content_str and not texts: # 避免重复添加
             texts.append(body_content_str)


    extracted_text = " ".join(filter(None, texts)).strip()

    if image_key:
        # 调用 get_message_image_content 获取图片的 base64 内容
        # 注意：get_message_image_content 内部会处理 API 调用和 base64 编码
        logger.info(f"为消息 {message_id} 获取图片内容, image_key: {image_key}")
        image_url_b64 = get_message_image_content(message_id, image_key)
        if image_url_b64 is None:
            logger.warning(f"未能为消息 {message_id} 的 image_key {image_key} 获取到图片内容")


    logger.info(f"解析后消息内容 - 文本: '{extracted_text[:100]}...', 是否存在图片: {bool(image_url_b64)}")
    return MessageContent(text=extracted_text, image_url=f"data:image/jpeg;base64,{image_url_b64}" if bool(image_url_b64) else None)


def get_message_image_content(message_id: str, file_key: str) -> str | None:
    """获取消息图片内容"""
    logger.info(f"获取消息图片内容: {message_id}, {file_key}")
    request: GetMessageResourceRequest = (
        GetMessageResourceRequest.builder()
        .message_id(message_id)
        .file_key(file_key)
        .type("image")
        .build()
    )
    response: GetMessageResourceResponse = lark_client.im.v1.message_resource.get(request)
    if not response.success():
        logger.exception(f"获取消息图片内容失败: {response.raw.content}")
        return None
    
    # 读取图片文件并转换为base64编码
    return base64.b64encode(response.file.read()).decode('utf-8')

def reply_simple_text_message(message_id: str, text: str):
    """回复简单文本消息"""
    # 正确创建请求对象
    req: ReplyMessageRequest = (
        ReplyMessageRequest.builder()
        .message_id(message_id)
        .request_body(
            ReplyMessageRequestBody.builder()
            .content(json.dumps({"text": text}))
            .msg_type("text")
            .reply_in_thread(False)
            .uuid(str(uuid.uuid4()))
            .build()
        )
        .build()
    )

    logger.info(f"reply_simple_text_message to {message_id}: {text}")
    # 发送请求
    response: ReplyMessageResponse = lark_client.im.v1.message.reply(request=req)
    logger.info(f"reply_simple_text_message response: {response.raw.content}")


def get_update_footnote(chat_id: str) -> str:  # 确保 chat_id 参数存在
    # 更新脚注内容以包含指向网页版的链接，使用查询参数而不是路径参数
    HOST_NAME = os.getenv("CHAT_BI_HOST_NAME", "https://chat-bi.summerfarm.net")
    return f"> [生成中...] 最后更新于:{datetime.now().strftime('%m-%d %H:%M:%S')}\n如果长时间未更新，[请点击此处在网页版查看结果]({HOST_NAME}/?chat={chat_id})"


def get_markdown_post_object(
    markdown_content: str,
    title: str = "来自ChatBI的回复",
    chat_id: str = None,
    user_id: str = None,
) -> str:
    """获取Markdown消息对象
    https://open.feishu.cn/document/feishu-cards/card-json-v2-structure
    """
    footnote = (
        get_update_footnote(chat_id)
        if chat_id
        else f"> [生成中...] 最后更新于:{datetime.now().strftime('%m-%d %H:%M:%S')}"
    )
    if LOG_SERVER_NAME:
        footnote += f"\n> [服务器: {SERVER_NAME}]"

    content_json = {
        "schema": "2.0",
        "config": {
            "wide_screen_mode": True,
            "streaming_mode": True,
            "streaming_config": {
                "print_frequency_ms": {
                    "default": 50,
                },
                "print_step": {"default": 5},
                "print_strategy": "fast",
            },
            "width_mode": "fill",
            "enable_forward": True,
        },
        "body": {
            "elements": [
                {
                    "tag": "collapsible_panel",
                    "expanded": False,
                    "header": {
                        "title": {
                            "tag": "plain_text",
                            "content": "🤖思考过程(可点击展开)",
                        },
                        "vertical_align": "center",
                        "icon": {
                            "tag": "standard_icon",
                            "token": "down-small-ccm_outlined",
                            "color": "",
                            "size": "16px 16px",
                        },
                        "icon_position": "right",
                        "icon_expanded_angle": -180,
                    },
                    "border": {"color": "grey", "corner_radius": "5px"},
                    "vertical_spacing": "8px",
                    "padding": "8px 8px 8px 8px",
                    "elements": [
                        {
                            "tag": "markdown",
                            "content": markdown_content,
                            "element_id": THINKING_LEMENT_ID,
                        }
                    ],
                },
                {
                    "tag": "markdown",
                    "content": THINKING_PROCESS_SPLITTER,
                },
                {
                    "tag": "markdown",
                    "content": "☕️ 请稍等...",
                    "element_id": FINAL_LEMENT_ID,
                },
                {
                    "tag": "button",
                    "element_id": FEEDBACK_ELEMENT_ID,
                    "type": "primary",
                    "size": "small",
                    "text": {"tag": "plain_text", "content": "数据不准👎"},
                    "disabled": True,
                    "disabled_tips": {},
                    "behaviors": [
                        {"type": "callback", "value": {"conversation_id": f"{chat_id}"}}
                    ],
                },
                {
                    "tag": "markdown",
                    "content": footnote,
                    "element_id": FOOTNOTE_ELEMENT_ID,
                },
            ]
        },
        "header": {
            "template": "blue",
            "title": {"content": f"<at id={user_id}></at> {title}", "tag": "lark_md"},
        },
    }
    return json.dumps(content_json, ensure_ascii=False)


def initial_card_message(
    message_id: str,
    card_content: str = "您的消息已收到，正在处理...",
    user_query: str = None,
    chat_id: str = None,
    user_id: str = None,
) -> tuple[str, str]:
    """初始化卡片消息
    返回(card_id, element_id) 的组合"""

    # 构造创建Card请求对象
    request: CreateCardRequest = (
        CreateCardRequest.builder()
        .request_body(
            CreateCardRequestBody.builder()
            .type("card_json")
            .data(
                get_markdown_post_object(
                    card_content,
                    title=f"ChatBI回复: {user_query}",
                    chat_id=chat_id,
                    user_id=user_id,
                )
            )
            .build()
        )
        .build()
    )

    # 发起创建Card请求
    logger.info(
        f"Creating card for message {message_id} with content: {lark.JSON.marshal(request.body)}"
    )
    response: CreateCardResponse = lark_client.cardkit.v1.card.create(request)
    if not response.success():
        logger.error(f"Failed to create card: {response.raw.content}")
        return None, None
    card_id = response.data.card_id
    logger.info(f"Card created successfully: {card_id}")

    # 构造消息请求对象
    request: ReplyMessageRequest = (
        ReplyMessageRequest.builder()
        .message_id(message_id)
        .request_body(
            ReplyMessageRequestBody.builder()
            .content(json.dumps({"type": "card", "data": {"card_id": card_id}}))
            .msg_type("interactive")
            .reply_in_thread(False)
            .uuid(str(uuid.uuid4()))
            .build()
        )
        .build()
    )

    # 发送消息的请求
    response: ReplyMessageResponse = lark_client.im.v1.message.reply(request)

    # 处理失败返回
    if not response.success():
        lark.logger.error(f"send card failed, code resp: {response.raw.content}")
        return None, None  # Return None if failed

    # 处理业务结果
    logger.info(f"send card success, code resp: {lark.JSON.marshal(response.data)}")

    return (
        card_id,
        THINKING_LEMENT_ID,
    )


def send_updates_to_card(
    card_id: str,
    markdown_content: str,
    element_id: str,  # 主内容（通常是思考过程）的元素ID
    sequence: int = 0,  # 初始序列号
) -> int:
    if not markdown_content:
        return sequence
    # 移除markdown_content前面的多余换行符
    markdown_content = markdown_content.lstrip("\n")

    # 构造最终答案更新请求
    final_answer_request: ContentCardElementRequest = (
        ContentCardElementRequest.builder()
        .card_id(card_id)
        .element_id(element_id)
        .request_body(
            ContentCardElementRequestBody.builder()
            .uuid(str(uuid.uuid4()))
            .content(markdown_content)
            .sequence(sequence)
            .build()
        )
        .build()
    )
    response: ContentCardElementResponse = lark_client.cardkit.v1.card_element.content(
        final_answer_request
    )
    if not response.success():
        logger.warning(
            f"Update card element failed (seq: {sequence}), card_id:{card_id}:{element_id}. Response: {response.raw.content}"
        )

    return sequence + 1  # 返回最终的序列号


def after_badcase_mark(card_id: str) -> int:
    if not card_id:
        logger.error("card_id is empty")
        return 0
    post_json = {
        "disabled": True,
        "text": {"content": "您已标记为数据不准案例，感谢您的反馈！"},
    }
    request: PatchCardElementRequest = (
        PatchCardElementRequest.builder()
        .card_id(card_id)
        .element_id(FEEDBACK_ELEMENT_ID)
        .request_body(
            PatchCardElementRequestBody.builder()
            .partial_element(json.dumps(post_json, ensure_ascii=False))
            .uuid(str(uuid.uuid4()))
            .sequence(5000)  # 使用非常大的序列号，确保覆盖之前的所有操作
            .build()
        )
        .build()
    )
    response: ContentCardElementResponse = lark_client.cardkit.v1.card_element.patch(
        request
    )
    logger.info(f"response: {response.raw.content}")
    return response.code


def send_finished_message_to_card(card_id: str, chat_id: str, sequence: int) -> int:
    """更新脚注到最终状态，然后发送最终设置更新"""
    final_footnote_content = f"> 任务完成于:{datetime.now().strftime('%m-%d %H:%M:%S')}"
    logger.info(final_footnote_content)

    # 使用同步方式更新脚注
    request: ContentCardElementRequest = (
        ContentCardElementRequest.builder()
        .card_id(card_id)
        .element_id(FOOTNOTE_ELEMENT_ID)
        .request_body(
            ContentCardElementRequestBody.builder()
            .uuid(str(uuid.uuid4()))
            .content(final_footnote_content)
            .sequence(sequence)
            .build()
        )
        .build()
    )
    response: ContentCardElementResponse = lark_client.cardkit.v1.card_element.content(
        request
    )
    logger.info(
        f"Footnote update response: {response.raw.content}, sequence: {sequence}"
    )
    sequence += 1

    update_card_patch = {
        "disabled": False,
        "behaviors": [
            {
                "type": "callback",
                "value": {"conversation_id": f"{chat_id}", "card_id": card_id},
            }
        ],
    }
    request: PatchCardElementRequest = (
        PatchCardElementRequest.builder()
        .card_id(card_id)
        .element_id(FEEDBACK_ELEMENT_ID)
        .request_body(
            PatchCardElementRequestBody.builder()
            .partial_element(json.dumps(update_card_patch, ensure_ascii=False))
            .uuid(str(uuid.uuid4()))
            .sequence(sequence)
            .build()
        )
        .build()
    )
    response: ContentCardElementResponse = lark_client.cardkit.v1.card_element.patch(
        request
    )
    logger.info(
        f"Update feedback element response: {response.raw.content}, sequence: {sequence}"
    )
    sequence += 1

    logger.info(
        f"Sending final settings update for card: {card_id}, sequence: {sequence}"
    )
    json_str = json.dumps(
        {
            "config": {
                "enable_forward": True,
                "enable_forward_interaction": False,
                "streaming_mode": False,
                "update_multi": True,
                "width_mode": "fill",
            }
        }
    )
    # 构造请求对象
    request: SettingsCardRequest = (
        SettingsCardRequest.builder()
        .card_id(card_id)
        .request_body(
            SettingsCardRequestBody.builder()
            .settings(json_str)
            .uuid(str(uuid.uuid4()))
            .sequence(sequence)
            .build()
        )
        .build()
    )

    # 发起请求
    response: SettingsCardResponse = lark_client.cardkit.v1.card.settings(request)
    logger.info(f"settings card response: {response.raw.content}")
    return sequence + 1


def send_message_to_chat(chat_id: str, message: str, msg_type: str = "text") -> bool:
    """
    发送消息到指定的飞书群聊

    Args:
        chat_id (str): 群聊ID
        message (str): 要发送的消息内容
        msg_type (str): 消息类型，默认为"text"

    Returns:
        bool: 发送成功返回True，失败返回False
    """
    if not chat_id:
        logger.warning("群聊ID为空，无法发送消息")
        return False

    logger.info(f"准备发送消息到chat_id: {chat_id}: {message[:50]}...")


    try:
        # 根据消息类型构造不同的内容
        if msg_type == "text":
            content = json.dumps({"text": message})
        else:
            content = message  # 对于interactive类型，message已经是JSON字符串

        # 构造发送消息请求
        request: CreateMessageRequest = (
            CreateMessageRequest.builder()
            .receive_id_type("chat_id")
            .request_body(
                CreateMessageRequestBody.builder()
                .receive_id(chat_id)
                .content(content)
                .msg_type(msg_type)
                .uuid(str(uuid.uuid4()))
                .build()
            )
            .build()
        )

        # 发送消息
        response: CreateMessageResponse = lark_client.im.v1.message.create(request)

        if response.success():
            logger.info(f"成功发送消息到chat_id：{chat_id}")
            return True
        else:
            logger.error(f"发送消息到群聊失败: {response.raw.content}")
            return False

    except Exception as e:
        logger.error(f"发送消息到群聊时发生异常: {str(e)}", exc_info=True)
        return False


def send_bad_case_notification(conversation_id: str, user_name: str = None, user_message: str = None) -> bool:
    """
    发送Bad Case标记通知到指定群聊

    Args:
        conversation_id (str): 对话ID
        user_name (str, optional): 用户名称
        user_message (str, optional): 用户最后一条消息内容

    Returns:
        bool: 发送成功返回True，失败返回False
    """
    if not BAD_CASE_NOTIFICATION_CHAT_ID:
        logger.warning("未配置Bad Case通知群聊ID，跳过发送通知")
        return False

    # 构造通知消息
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    user_info = f"**用户**: {user_name}" if user_name else "**用户**: 未知用户"

    # 获取主机名用于构造Dashboard链接
    HOST_NAME = os.getenv("CHAT_BI_HOST_NAME", "https://chat-bi.summerfarm.net")
    dashboard_link = f"{HOST_NAME}/dashboard?chat={conversation_id}"

    # 构造用户消息内容部分
    user_message_section = ""
    if user_message:
        # 限制消息长度，避免通知过长
        max_length = 200
        if len(user_message) > max_length:
            truncated_message = user_message[:max_length] + "..."
        else:
            truncated_message = user_message

        user_message_section = f"""
**用户消息内容**:
> {truncated_message}
"""

    # 构造Markdown内容
    markdown_content = f"""{user_info}
**对话ID**: `{conversation_id}`
**标记时间**: {timestamp}{user_message_section}

**操作**: [📊 查看对话详情]({dashboard_link})

---
请相关人员及时关注和处理。"""

    # 构造飞书交互式卡片消息
    card_content = {
        "header": {
            "template": "red",
            "title": {
                "content": "🚨 Bad Case 标记通知",
                "tag": "lark_md",
            },
        },
        "elements": [
            {
                "tag": "markdown",
                "content": markdown_content,
            }
        ],
    }

    return send_message_to_chat(BAD_CASE_NOTIFICATION_CHAT_ID, json.dumps(card_content), msg_type="interactive")


async def send_recommendation_card(chat_id: str, user_name: str, recommendations: list[str]) -> bool:
    """
    发送推荐问题卡片到指定聊天

    Args:
        chat_id (str): 聊天ID
        user_name (str): 用户姓名
        recommendations (list[str]): 推荐问题列表

    Returns:
        bool: 发送成功返回True，失败返回False
    """
    if not chat_id or not recommendations:
        logger.warning("聊天ID或推荐内容为空，无法发送推荐卡片")
        return False

    try:
        # 构建推荐问题的Markdown内容
        recommendation_text = "基于您的历史查询和其他用户的使用情况，我为您推荐以下问题：\n\n"
        for i, question in enumerate(recommendations, 1):
            recommendation_text += f"{i}. {question}\n"

        # 构造推荐卡片内容，使用正确的飞书卡片格式
        feishu_message_obj = {
            "schema": "2.0",
            "header": {
                "template": "blue",
                "title": {
                    "content": f"**👏 {user_name}，欢迎回来，为您推荐以下问题**",
                    "tag": "lark_md",
                },
            },
            "body": {
                "elements": [
                    {
                        "tag": "markdown",
                        "content": recommendation_text,
                    },
                    {
                        "tag": "markdown",
                        "content": "[👉新手必读：当机器人反问了我一个问题时该怎么办？](https://summerfarm.feishu.cn/wiki/KGCuwV2JeiGEsBku0kicEaEznuf)",
                    },
                    {
                        "tag": "markdown",
                        "content": f"> 推荐时间: {datetime.now().strftime('%Y-%m-%d %H:%M')}\n> ",
                    },
                ]
            },
        }

        logger.info(f"准备发送推荐卡片到聊天 {chat_id}，包含 {len(recommendations)} 条推荐")
        return send_message_to_chat(chat_id, json.dumps(feishu_message_obj), msg_type="interactive")

    except Exception as e:
        logger.error(f"发送推荐卡片时发生异常: {str(e)}", exc_info=True)
        return False


def send_bad_case_unmark_notification(conversation_id: str, user_name: str = None, user_message: str = None) -> bool:
    """
    发送Bad Case取消标记通知到指定群聊

    Args:
        conversation_id (str): 对话ID
        user_name (str, optional): 用户名称
        user_message (str, optional): 用户最后一条消息内容

    Returns:
        bool: 发送成功返回True，失败返回False
    """
    if not BAD_CASE_NOTIFICATION_CHAT_ID:
        logger.warning("未配置Bad Case通知群聊ID，跳过发送通知")
        return False

    # 构造通知消息
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    user_info = f"**用户**: {user_name}" if user_name else "**用户**: 未知用户"

    # 获取主机名用于构造Dashboard链接
    HOST_NAME = os.getenv("CHAT_BI_HOST_NAME", "https://chat-bi.summerfarm.net")
    dashboard_link = f"{HOST_NAME}/dashboard?chat={conversation_id}"

    # 构造用户消息内容部分
    user_message_section = ""
    if user_message:
        # 限制消息长度，避免通知过长
        max_length = 200
        if len(user_message) > max_length:
            truncated_message = user_message[:max_length] + "..."
        else:
            truncated_message = user_message

        user_message_section = f"""
**用户消息内容**:
> {truncated_message}
"""

    # 构造Markdown内容
    markdown_content = f"""{user_info}
**对话ID**: `{conversation_id}`
**取消标记时间**: {timestamp}{user_message_section}

**操作**: [📊 查看对话详情]({dashboard_link})

---
该对话已被取消Bad Case标记。"""

    # 构造飞书交互式卡片消息
    card_content = {
        "header": {
            "template": "green",
            "title": {
                "content": "✅ Bad Case 取消标记通知",
                "tag": "lark_md",
            },
        },
        "elements": [
            {
                "tag": "markdown",
                "content": markdown_content,
            }
        ],
    }

    return send_message_to_chat(BAD_CASE_NOTIFICATION_CHAT_ID, json.dumps(card_content), msg_type="interactive")
